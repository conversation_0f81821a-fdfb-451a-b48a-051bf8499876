import { register } from 'node:module';
import { pathToFileURL } from 'node:url';

// Register ts-node for TypeScript support
register('ts-node/esm', pathToFileURL('./'));

// Custom loader for .ts files
export async function resolve(specifier, context, defaultResolve) {
  if (specifier.endsWith('.ts') && !specifier.endsWith('.d.ts')) {
    return {
      url: specifier,
      format: 'module'
    };
  }
  return defaultResolve(specifier, context);
}

export async function load(url, context, defaultLoad) {
  if (url.endsWith('.ts') && !url.endsWith('.d.ts')) {
    return {
      format: 'module',
      source: await defaultLoad(url, { ...context, format: 'module' }).then(result => result.source)
    };
  }
  return defaultLoad(url, context);
}
