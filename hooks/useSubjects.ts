import { useState, useEffect } from 'react';
import { Subject } from '@/types/app';
import { Platform } from 'react-native';

const STORAGE_KEY = 'isotope_subjects';

export function useSubjects() {
  const [subjects, setSubjects] = useState<Subject[]>([]);

  useEffect(() => {
    loadSubjects();
  }, []);

  const loadSubjects = async () => {
    try {
      if (Platform.OS === 'web') {
        const stored = localStorage.getItem(STORAGE_KEY);
        if (stored) {
          const parsed = JSON.parse(stored);
          setSubjects(parsed.map((s: any) => ({
            ...s,
            createdAt: new Date(s.createdAt),
          })));
        }
      }
    } catch (error) {
      console.error('Error loading subjects:', error);
    }
  };

  const saveSubjects = async (newSubjects: Subject[]) => {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newSubjects));
      }
      setSubjects(newSubjects);
    } catch (error) {
      console.error('Error saving subjects:', error);
    }
  };

  const addSubject = (subjectData: Omit<Subject, 'id' | 'createdAt'>) => {
    const newSubject: Subject = {
      id: Date.now().toString(),
      ...subjectData,
      createdAt: new Date(),
    };
    const newSubjects = [...subjects, newSubject];
    saveSubjects(newSubjects);
  };

  const updateSubject = (id: string, updates: Partial<Subject>) => {
    const newSubjects = subjects.map(subject =>
      subject.id === id ? { ...subject, ...updates } : subject
    );
    saveSubjects(newSubjects);
  };

  const deleteSubject = (id: string) => {
    const newSubjects = subjects.filter(subject => subject.id !== id);
    saveSubjects(newSubjects);
  };

  const getSubjectById = (id: string) => {
    return subjects.find(subject => subject.id === id);
  };

  return {
    subjects,
    addSubject,
    updateSubject,
    deleteSubject,
    getSubjectById,
  };
}