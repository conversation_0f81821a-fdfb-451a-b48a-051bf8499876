{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "jsx": "react-native", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "module": "ESNext", "target": "ES2022", "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "paths": {"@/*": ["./*"]}}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts"], "exclude": ["node_modules", "node_modules/expo-modules-core/src/index.ts"]}