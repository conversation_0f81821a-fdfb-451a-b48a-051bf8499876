export interface Subject {
  id: string;
  name: string;
  color: string;
  createdAt: Date;
}

export interface TimerSession {
  id: string;
  subjectId?: string;
  startTime: Date;
  endTime?: Date;
  duration: number; // in seconds
  mode: 'stopwatch' | 'pomodoro';
  isBreak?: boolean;
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  targetDate: Date;
  subjectId?: string;
  priority: 'low' | 'medium' | 'high';
  completed: boolean;
  createdAt: Date;
}

export interface PomodoroSettings {
  workDuration: number; // in minutes
  shortBreakDuration: number; // in minutes
  longBreakDuration: number; // in minutes
  sessionsUntilLongBreak: number;
}

export interface BlockedApp {
  id: string;
  name: string;
  packageName: string; // For mobile apps
  icon: string;
  category: 'social' | 'entertainment' | 'gaming' | 'news' | 'shopping' | 'other';
  isBlocked: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
  customSchedule?: BlockingSchedule[];
}

export interface BlockedWebsite {
  id: string;
  name: string;
  url: string;
  domain: string;
  category: 'social' | 'entertainment' | 'gaming' | 'news' | 'shopping' | 'other';
  isBlocked: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
  customSchedule?: BlockingSchedule[];
}

export interface BlockingSchedule {
  id: string;
  name: string;
  startTime: string; // HH:MM format
  endTime: string; // HH:MM format
  daysOfWeek: number[]; // 0-6, Sunday = 0
  isActive: boolean;
}

export interface DistractionAttempt {
  id: string;
  appId?: string;
  websiteId?: string;
  appName: string;
  timestamp: Date;
  sessionId?: string; // Link to timer session
  wasBlocked: boolean;
  blockReason: 'focus_session' | 'break_time' | 'custom_schedule' | 'manual_block';
  duration?: number; // How long user tried to access (in seconds)
}

export interface DistractionStats {
  totalAttempts: number;
  blockedAttempts: number;
  allowedAttempts: number;
  mostBlockedApp: string;
  mostBlockedWebsite: string;
  totalTimeBlocked: number; // in seconds
  streakDays: number;
  lastUpdated: Date;
}

export interface BlockingSettings {
  isEnabled: boolean;
  blockDuringFocus: boolean;
  blockDuringBreaks: boolean;
  allowEmergencyAccess: boolean;
  emergencyAccessDuration: number; // in minutes
  showBlockingNotifications: boolean;
  strictMode: boolean; // Prevents disabling during active sessions
  whitelistedApps: string[]; // App IDs that are never blocked
  whitelistedWebsites: string[]; // Website IDs that are never blocked
}