import { Platform, AppState, AppStateStatus } from 'react-native';
import { distractionBlockingService } from './distractionBlockingService';
import { notificationService } from './notificationService';

interface AppUsageEvent {
  appName: string;
  packageName: string;
  timestamp: Date;
  action: 'opened' | 'closed' | 'blocked';
}

class MobileAppMonitor {
  private isMonitoring = false;
  private currentContext: 'focus' | 'break' | 'normal' = 'normal';
  private appStateSubscription: any = null;
  private usageEvents: AppUsageEvent[] = [];
  private lastAppState: AppStateStatus = 'active';

  constructor() {
    this.handleAppStateChange = this.handleAppStateChange.bind(this);
  }

  // Start monitoring app usage
  startMonitoring() {
    if (this.isMonitoring) return;

    this.isMonitoring = true;
    
    // Listen to app state changes
    this.appStateSubscription = AppState.addEventListener('change', this.handleAppStateChange);
    
    console.log('Mobile app monitoring started');
  }

  // Stop monitoring app usage
  stopMonitoring() {
    if (!this.isMonitoring) return;

    this.isMonitoring = false;
    
    if (this.appStateSubscription) {
      this.appStateSubscription.remove();
      this.appStateSubscription = null;
    }
    
    console.log('Mobile app monitoring stopped');
  }

  // Set the current focus context
  setContext(context: 'focus' | 'break' | 'normal') {
    this.currentContext = context;
    console.log(`App monitor context set to: ${context}`);
  }

  // Handle app state changes
  private async handleAppStateChange(nextAppState: AppStateStatus) {
    if (Platform.OS === 'web') return; // Only for mobile platforms

    const previousState = this.lastAppState;
    this.lastAppState = nextAppState;

    // Detect when user leaves our app (potentially to open another app)
    if (previousState === 'active' && nextAppState === 'background') {
      await this.handleAppSwitch();
    }

    // Detect when user returns to our app
    if (previousState === 'background' && nextAppState === 'active') {
      await this.handleAppReturn();
    }
  }

  // Handle when user switches away from our app
  private async handleAppSwitch() {
    if (!this.isMonitoring || this.currentContext === 'normal') return;

    // Record potential distraction attempt
    const event: AppUsageEvent = {
      appName: 'Unknown App',
      packageName: 'unknown',
      timestamp: new Date(),
      action: 'opened',
    };

    this.usageEvents.push(event);

    // Show warning notification
    await notificationService.showWarningNotification(
      '⚠️ You switched away from your focus session. Stay focused! 🎯'
    );

    // Record distraction attempt
    await distractionBlockingService.recordDistractionAttempt(
      'Unknown App',
      this.currentContext,
      undefined, // sessionId
      'unknown'
    );
  }

  // Handle when user returns to our app
  private async handleAppReturn() {
    if (!this.isMonitoring || this.currentContext === 'normal') return;

    // Show encouragement notification
    await notificationService.showSuccessNotification(
      '✅ Welcome back! Keep up the great focus! 💪'
    );
  }

  // Simulate blocking a specific app (for demo purposes)
  async simulateAppBlock(appName: string, packageName: string): Promise<boolean> {
    const blockedApps = await distractionBlockingService.getBlockedApps();
    const isBlocked = blockedApps.some(app => 
      app.name.toLowerCase() === appName.toLowerCase() || 
      app.packageName === packageName
    );

    if (isBlocked && this.currentContext !== 'normal') {
      // Record blocked attempt
      const event: AppUsageEvent = {
        appName,
        packageName,
        timestamp: new Date(),
        action: 'blocked',
      };

      this.usageEvents.push(event);

      // Show blocking notification
      await notificationService.showBlockingNotification(
        appName,
        this.currentContext === 'focus' ? 'focus_session' : 'break_time'
      );

      // Record distraction attempt
      await distractionBlockingService.recordDistractionAttempt(
        appName,
        this.currentContext,
        undefined, // sessionId
        packageName
      );

      return true; // App was blocked
    }

    return false; // App was not blocked
  }

  // Get recent usage events
  getRecentEvents(limit: number = 10): AppUsageEvent[] {
    return this.usageEvents.slice(-limit);
  }

  // Clear usage events
  clearEvents() {
    this.usageEvents = [];
  }

  // Check if monitoring is active
  isActive(): boolean {
    return this.isMonitoring;
  }

  // Get current context
  getCurrentContext(): 'focus' | 'break' | 'normal' {
    return this.currentContext;
  }

  // Enable/disable monitoring based on blocking settings
  async updateMonitoringState() {
    const settings = distractionBlockingService.getBlockingSettings();
    
    if (settings.isEnabled && Platform.OS !== 'web') {
      this.startMonitoring();
    } else {
      this.stopMonitoring();
    }
  }
}

export const mobileAppMonitor = new MobileAppMonitor();
