import { Platform } from 'react-native';

export interface InstalledApp {
  packageName: string;
  appName: string;
  icon?: string;
  versionName?: string;
  versionCode?: number;
  isSystemApp?: boolean;
}

class InstalledAppsService {
  private cachedApps: InstalledApp[] = [];
  private lastFetchTime: number = 0;
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Check if we can access installed apps
  async canAccessInstalledApps(): Promise<boolean> {
    if (Platform.OS === 'web') {
      return false;
    }

    try {
      // Check if device supports app listing
      if (Platform.OS === 'android') {
        // On Android, we need special permissions
        return true; // Assume device capability, will handle errors gracefully
      } else if (Platform.OS === 'ios') {
        // On iOS, app listing is very restricted
        return false; // iOS doesn't allow listing other apps for privacy
      }
      return false;
    } catch (error) {
      console.warn('Error checking app access capability:', error);
      return false;
    }
  }

  // Get installed apps (Android only, with limitations)
  async getInstalledApps(): Promise<InstalledApp[]> {
    // Check cache first
    const now = Date.now();
    if (this.cachedApps.length > 0 && (now - this.lastFetchTime) < this.CACHE_DURATION) {
      return this.cachedApps;
    }

    try {
      if (Platform.OS === 'android') {
        // Note: This requires special permissions and may not work in all cases
        // For now, we'll return a mock list of common apps
        const commonApps = await this.getCommonAndroidApps();
        this.cachedApps = commonApps;
        this.lastFetchTime = now;
        return commonApps;
      } else if (Platform.OS === 'ios') {
        // iOS doesn't allow listing installed apps for privacy reasons
        const commonApps = await this.getCommonIOSApps();
        this.cachedApps = commonApps;
        this.lastFetchTime = now;
        return commonApps;
      } else {
        // Web platform
        return this.getCommonWebApps();
      }
    } catch (error) {
      console.warn('Error fetching installed apps:', error);
      // Fallback to common apps
      return this.getCommonAppsForPlatform();
    }
  }

  // Get common Android apps (since direct access is limited)
  private async getCommonAndroidApps(): Promise<InstalledApp[]> {
    return [
      { packageName: 'com.instagram.android', appName: 'Instagram', icon: '📷' },
      { packageName: 'com.facebook.katana', appName: 'Facebook', icon: '📘' },
      { packageName: 'com.twitter.android', appName: 'Twitter', icon: '🐦' },
      { packageName: 'com.tiktok.android', appName: 'TikTok', icon: '🎵' },
      { packageName: 'com.snapchat.android', appName: 'Snapchat', icon: '👻' },
      { packageName: 'com.whatsapp', appName: 'WhatsApp', icon: '💬' },
      { packageName: 'com.discord', appName: 'Discord', icon: '🎮' },
      { packageName: 'com.reddit.frontpage', appName: 'Reddit', icon: '🤖' },
      { packageName: 'com.youtube.android', appName: 'YouTube', icon: '📺' },
      { packageName: 'com.netflix.mediaclient', appName: 'Netflix', icon: '🎬' },
      { packageName: 'com.spotify.music', appName: 'Spotify', icon: '🎵' },
      { packageName: 'com.amazon.mShop.android.shopping', appName: 'Amazon', icon: '🛒' },
      { packageName: 'com.ubercab', appName: 'Uber', icon: '🚗' },
      { packageName: 'com.airbnb.android', appName: 'Airbnb', icon: '🏠' },
      { packageName: 'com.duolingo', appName: 'Duolingo', icon: '🦉' },
      { packageName: 'com.medium.reader', appName: 'Medium', icon: '📖' },
      { packageName: 'com.pinterest', appName: 'Pinterest', icon: '📌' },
      { packageName: 'com.tumblr', appName: 'Tumblr', icon: '📝' },
      { packageName: 'com.linkedin.android', appName: 'LinkedIn', icon: '💼' },
      { packageName: 'com.zhiliaoapp.musically', appName: 'TikTok', icon: '🎭' },
    ];
  }

  // Get common iOS apps
  private async getCommonIOSApps(): Promise<InstalledApp[]> {
    return [
      { packageName: 'com.burbn.instagram', appName: 'Instagram', icon: '📷' },
      { packageName: 'com.facebook.Facebook', appName: 'Facebook', icon: '📘' },
      { packageName: 'com.atebits.Tweetie2', appName: 'Twitter', icon: '🐦' },
      { packageName: 'com.zhiliaoapp.musically', appName: 'TikTok', icon: '🎵' },
      { packageName: 'com.toyopagroup.picaboo', appName: 'Snapchat', icon: '👻' },
      { packageName: 'net.whatsapp.WhatsApp', appName: 'WhatsApp', icon: '💬' },
      { packageName: 'com.hammerandchisel.discord', appName: 'Discord', icon: '🎮' },
      { packageName: 'com.reddit.Reddit', appName: 'Reddit', icon: '🤖' },
      { packageName: 'com.google.ios.youtube', appName: 'YouTube', icon: '📺' },
      { packageName: 'com.netflix.Netflix', appName: 'Netflix', icon: '🎬' },
      { packageName: 'com.spotify.client', appName: 'Spotify', icon: '🎵' },
      { packageName: 'com.amazon.Amazon', appName: 'Amazon', icon: '🛒' },
      { packageName: 'com.ubercab.UberClient', appName: 'Uber', icon: '🚗' },
      { packageName: 'com.airbnb.app', appName: 'Airbnb', icon: '🏠' },
      { packageName: 'com.duolingo.DuolingoMobile', appName: 'Duolingo', icon: '🦉' },
      { packageName: 'com.medium.reader', appName: 'Medium', icon: '📖' },
      { packageName: 'pinterest', appName: 'Pinterest', icon: '📌' },
      { packageName: 'com.tumblr.tumblr', appName: 'Tumblr', icon: '📝' },
      { packageName: 'com.linkedin.LinkedIn', appName: 'LinkedIn', icon: '💼' },
      { packageName: 'com.apple.mobilesafari', appName: 'Safari', icon: '🌐' },
    ];
  }

  // Get common web apps/sites
  private getCommonWebApps(): InstalledApp[] {
    return [
      { packageName: 'instagram.com', appName: 'Instagram', icon: '📷' },
      { packageName: 'facebook.com', appName: 'Facebook', icon: '📘' },
      { packageName: 'twitter.com', appName: 'Twitter', icon: '🐦' },
      { packageName: 'tiktok.com', appName: 'TikTok', icon: '🎵' },
      { packageName: 'web.whatsapp.com', appName: 'WhatsApp Web', icon: '💬' },
      { packageName: 'discord.com', appName: 'Discord', icon: '🎮' },
      { packageName: 'reddit.com', appName: 'Reddit', icon: '🤖' },
      { packageName: 'youtube.com', appName: 'YouTube', icon: '📺' },
      { packageName: 'netflix.com', appName: 'Netflix', icon: '🎬' },
      { packageName: 'open.spotify.com', appName: 'Spotify Web', icon: '🎵' },
      { packageName: 'amazon.com', appName: 'Amazon', icon: '🛒' },
      { packageName: 'medium.com', appName: 'Medium', icon: '📖' },
      { packageName: 'pinterest.com', appName: 'Pinterest', icon: '📌' },
      { packageName: 'tumblr.com', appName: 'Tumblr', icon: '📝' },
      { packageName: 'linkedin.com', appName: 'LinkedIn', icon: '💼' },
    ];
  }

  // Fallback method for common apps
  private getCommonAppsForPlatform(): InstalledApp[] {
    if (Platform.OS === 'android') {
      return this.getCommonAndroidApps();
    } else if (Platform.OS === 'ios') {
      return this.getCommonIOSApps();
    } else {
      return this.getCommonWebApps();
    }
  }

  // Search apps by name
  async searchApps(query: string): Promise<InstalledApp[]> {
    const apps = await this.getInstalledApps();
    const lowercaseQuery = query.toLowerCase();
    
    return apps.filter(app => 
      app.appName.toLowerCase().includes(lowercaseQuery) ||
      app.packageName.toLowerCase().includes(lowercaseQuery)
    );
  }

  // Get app suggestions based on category
  getAppSuggestionsByCategory(category: 'social' | 'entertainment' | 'productivity' | 'shopping' | 'all'): InstalledApp[] {
    const allApps = this.getCommonAppsForPlatform();
    
    const categoryMap = {
      social: ['Instagram', 'Facebook', 'Twitter', 'TikTok', 'Snapchat', 'WhatsApp', 'Discord', 'Reddit', 'LinkedIn'],
      entertainment: ['YouTube', 'Netflix', 'Spotify', 'TikTok'],
      productivity: ['Medium', 'Duolingo', 'LinkedIn'],
      shopping: ['Amazon', 'Airbnb'],
      all: allApps.map(app => app.appName)
    };

    const categoryApps = categoryMap[category] || categoryMap.all;
    return allApps.filter(app => categoryApps.includes(app.appName));
  }

  // Clear cache
  clearCache(): void {
    this.cachedApps = [];
    this.lastFetchTime = 0;
  }

  // Get device info for better app suggestions
  async getDeviceInfo() {
    try {
      return {
        platform: Platform.OS,
        deviceType: 'mobile',
        deviceName: 'Mobile Device',
        osName: Platform.OS,
        osVersion: 'unknown',
        isDevice: Platform.OS !== 'web',
        applicationName: 'IsotopeAI Focus',
        applicationId: 'com.isotopeai.focus',
      };
    } catch (error) {
      console.warn('Error getting device info:', error);
      return {
        platform: Platform.OS,
        deviceType: null,
        deviceName: null,
        osName: null,
        osVersion: null,
        isDevice: null,
        applicationName: null,
        applicationId: null,
      };
    }
  }
}

export const installedAppsService = new InstalledAppsService();
